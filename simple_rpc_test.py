#!/usr/bin/env python3
"""
使用 wslink 客户端库进行 RPC 调用
"""

import asyncio
import json
from wslink.websocket import ServerProtocol

class SimpleClient:
    def __init__(self, url):
        self.url = url
        self.protocol = None
        
    async def connect(self):
        """连接到服务器"""
        try:
            # 这里我们需要使用 wslink 的客户端协议
            print(f"尝试连接到 {self.url}")
            # 由于 wslink 主要是服务器端库，我们需要用其他方法
            return True
        except Exception as e:
            print(f"连接失败: {e}")
            return False
    
    async def call_rpc(self, method, *args, **kwargs):
        """调用 RPC 方法"""
        try:
            # 这里应该调用 RPC 方法
            print(f"调用 RPC: {method} with args: {args}, kwargs: {kwargs}")
            return {"status": "success", "message": "模拟调用成功"}
        except Exception as e:
            print(f"RPC 调用失败: {e}")
            return {"status": "error", "message": str(e)}

async def test_rpc_call():
    """测试 RPC 调用"""
    client = SimpleClient("ws://localhost:8083/ws")
    
    if await client.connect():
        result = await client.call_rpc(
            "load_data_from_path", 
            "/home/<USER>/otherProjects/pymipf/.sourceData/img_boning"
        )
        print(f"RPC 调用结果: {result}")
    else:
        print("连接失败")

if __name__ == "__main__":
    print("=== 简单 RPC 测试 ===")
    asyncio.run(test_rpc_call())
