import vtk

class VolumeClipper:
    def __init__(self, filename):
        self.reader = vtk.vtkXMLImageDataReader()
        self.reader.SetFileName(filename)
        self.reader.Update()

        # 体数据
        self.volume = self.reader.GetOutput()

        # Box 选择器
        self.boxWidget = vtk.vtkBoxWidget()
        self.boxWidget.SetPlaceFactor(1.25)  # 控制 Box 大小
        self.boxWidget.SetInputData(self.volume)

        # 体数据映射
        self.volume_mapper = vtk.vtkSmartVolumeMapper()
        self.volume_mapper.SetInputData(self.volume)
        self.volume_actor = vtk.vtkVolume()
        self.volume_actor.SetMapper(self.volume_mapper)

        # 交互界面
        self.ren = vtk.vtkRenderer()
        self.renWin = vtk.vtkRenderWindow()
        self.renWin.AddRenderer(self.ren)
        self.iren = vtk.vtkRenderWindowInteractor()
        self.iren.SetRenderWindow(self.renWin)

        # 添加 Box Widget
        self.boxWidget.SetInteractor(self.iren)
        self.boxWidget.PlaceWidget()

        # 绑定交互事件
        self.boxWidget.AddObserver("InteractionEvent", self.update_clip)

        # 设置初始视角
        self.ren.AddActor(self.volume_actor)
        self.ren.ResetCamera()

    def update_clip(self, caller, event):
        """更新 Box 位置并裁剪 Volume"""
        planes = vtk.vtkPlanes()
        self.boxWidget.GetPlanes(planes)

        extract = vtk.vtkExtractGeometry()
        extract.SetInputData(self.volume)
        extract.SetImplicitFunction(planes)
        extract.Update()

        # 更新 Volume
        self.volume_mapper.SetInputData(extract.GetOutput())
        self.volume_mapper.Update()

        self.renWin.Render()

    def start(self):
        """启动渲染"""
        self.iren.Initialize()
        self.iren.Start()

if __name__ == "__main__":
    filename = "D:/dsa.vti"  # 体数据文件（.vti 格式）
    clipper = VolumeClipper(filename)
    clipper.start()
