#!/usr/bin/env python3
"""
使用wslink库连接trame服务器的客户端
需要安装: pip install wslink
"""

import asyncio
import argparse
from wslink import websocket


class TrameClient:
    def __init__(self, host="localhost", port=8080, auth_key="wslink-secret"):
        self.host = host
        self.port = port
        self.auth_key = auth_key
        self.client = None
    
    async def connect(self):
        """连接到trame服务器"""
        url = f"ws://{self.host}:{self.port}/ws"
        
        # 创建websocket客户端
        self.client = websocket.create_client()
        
        # 连接配置
        config = {
            'sessionURL': url,
            'secret': self.auth_key,
            'application': 'trame'  # 应用类型
        }
        
        try:
            # 建立连接
            await self.client.connect(config)
            print(f"成功连接到 {url}")
            return True
        except Exception as e:
            print(f"连接失败: {e}")
            return False
    
    async def load_data_from_path(self, path: str):
        """加载数据"""
        if not self.client:
            print("客户端未连接")
            return None
        
        try:
            # 调用服务器方法
            result = await self.client.call("load_data_from_path", [path])
            print(f"加载数据结果: {result}")
            return result
        except Exception as e:
            print(f"加载数据失败: {e}")
            return None
    
    async def set_state(self, state_dict):
        """设置状态"""
        if not self.client:
            print("客户端未连接")
            return None
        
        try:
            result = await self.client.call("state.set", [state_dict])
            return result
        except Exception as e:
            print(f"设置状态失败: {e}")
            return None
    
    async def get_state(self, keys=None):
        """获取状态"""
        if not self.client:
            print("客户端未连接")
            return None
        
        try:
            if keys:
                result = await self.client.call("state.get", [keys])
            else:
                result = await self.client.call("state.get", [])
            return result
        except Exception as e:
            print(f"获取状态失败: {e}")
            return None
    
    async def set_playback_state(self, is_playing: bool):
        """设置播放状态"""
        return await self.set_state({"is_playing": is_playing})
    
    async def set_playback_progress(self, progress: float):
        """设置播放进度 (0.0 - 1.0)"""
        return await self.set_state({"playback_progress": progress})
    
    async def set_playback_speed(self, speed: float):
        """设置播放速度"""
        return await self.set_state({"playback_speed": speed})
    
    async def set_ctp_parameters(self, cbv=None, cbf=None, mtt=None, ttp=None):
        """设置CTP参数"""
        params = {}
        if cbv is not None:
            params["cbv_value"] = cbv
        if cbf is not None:
            params["cbf_value"] = cbf
        if mtt is not None:
            params["mtt_value"] = mtt
        if ttp is not None:
            params["ttp_value"] = ttp
        
        if params:
            return await self.set_state(params)
    
    async def reset_camera(self):
        """重置相机"""
        try:
            return await self.client.call("viewport.camera.reset", [])
        except Exception as e:
            print(f"重置相机失败: {e}")
            return None
    
    async def disconnect(self):
        """断开连接"""
        if self.client:
            await self.client.disconnect()
            print("连接已断开")


# 简单的数据加载函数
async def load_data_simple(path: str, host="localhost", port=8080, auth_key="wslink-secret"):
    """简单的数据加载函数"""
    client = TrameClient(host, port, auth_key)
    
    if await client.connect():
        try:
            result = await client.load_data_from_path(path)
            return result
        finally:
            await client.disconnect()
    
    return None


# 上下文管理器版本
class TrameClientContext:
    def __init__(self, host="localhost", port=8080, auth_key="wslink-secret"):
        self.client = TrameClient(host, port, auth_key)
    
    async def __aenter__(self):
        await self.client.connect()
        return self.client
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        await self.client.disconnect()


# 使用示例
async def example_usage():
    """使用示例"""
    # 方法1: 简单加载数据
    path = "/home/<USER>/otherProjects/pymipf/.sourceData/img_boning"
    result = await load_data_simple(path)
    print(f"简单加载结果: {result}")
    
    # 方法2: 使用上下文管理器
    async with TrameClientContext() as client:
        # 加载数据
        await client.load_data_from_path(path)
        
        # 获取当前状态
        state = await client.get_state()
        print(f"当前状态: {state}")
        
        # 控制播放
        await client.set_playback_speed(1.5)
        await client.set_playback_state(True)
        
        # 等待一会儿
        await asyncio.sleep(3)
        
        # 暂停播放
        await client.set_playback_state(False)
        
        # 设置CTP参数
        await client.set_ctp_parameters(cbv=50.0, cbf=30.0)


# 命令行接口
def main():
    parser = argparse.ArgumentParser(description='Trame WebSocket客户端')
    parser.add_argument('--path', help='要加载的数据路径')
    parser.add_argument('--host', default='localhost', help='服务器主机')
    parser.add_argument('--port', type=int, default=8080, help='服务器端口')
    parser.add_argument('--auth-key', default='wslink-secret', help='认证密钥')
    parser.add_argument('--action', choices=['load', 'play', 'pause', 'reset'], 
                       default='load', help='要执行的操作')
    parser.add_argument('--speed', type=float, help='播放速度')
    parser.add_argument('--progress', type=float, help='播放进度 (0.0-1.0)')
    
    args = parser.parse_args()
    
    async def run():
        client = TrameClient(args.host, args.port, args.auth_key)
        
        if await client.connect():
            try:
                if args.action == 'load' and args.path:
                    result = await client.load_data_from_path(args.path)
                    print(f"加载结果: {result}")
                
                elif args.action == 'play':
                    if args.speed:
                        await client.set_playback_speed(args.speed)
                    if args.progress is not None:
                        await client.set_playback_progress(args.progress)
                    await client.set_playback_state(True)
                    print("播放已开始")
                
                elif args.action == 'pause':
                    await client.set_playback_state(False)
                    print("播放已暂停")
                
                elif args.action == 'reset':
                    await client.reset_camera()
                    print("相机已重置")
                
                return 0
            except Exception as e:
                print(f"操作失败: {e}")
                return 1
            finally:
                await client.disconnect()
        else:
            return 1
    
    exit_code = asyncio.run(run())
    exit(exit_code)


# 一行命令加载数据
async def quick_load(path="/home/<USER>/otherProjects/pymipf/.sourceData/img_boning"):
    """快速加载数据的函数"""
    return await load_data_simple(path)


if __name__ == "__main__":
    # 如果直接运行且没有命令行参数，执行快速加载
    import sys
    
    if len(sys.argv) == 1:
        # 直接运行，使用默认路径
        path = "/home/<USER>/otherProjects/pymipf/.sourceData/img_boning"
        result = asyncio.run(quick_load(path))
        print(f"快速加载完成: {result}")
    else:
        # 使用命令行参数
        main()