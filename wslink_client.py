#!/usr/bin/env python3
"""
使用 wslink 协议调用 trame RPC 方法
"""

import asyncio
import json
import websockets
import msgpack
from wslink.chunking import generate_chunks, UnChunker

class TrameRPCClient:
    """Trame RPC 客户端"""

    def __init__(self, url="ws://localhost:8083/ws", secret="wslink-secret"):
        self.url = url
        self.secret = secret
        self.websocket = None
        self.client_id = None
        self.message_id = 0
        self.unchunker = UnChunker()

    async def connect(self):
        """连接到 trame 服务器"""
        try:
            print(f"连接到 {self.url}...")
            self.websocket = await websockets.connect(self.url)
            print("WebSocket 连接成功!")

            # 发送 hello 消息进行认证
            hello_response = await self._send_hello()
            if hello_response.get("error"):
                print(f"认证失败: {hello_response['error']}")
                return False

            self.client_id = hello_response.get("result", {}).get("clientID", "client")
            print(f"认证成功，客户端ID: {self.client_id}")
            return True

        except Exception as e:
            print(f"连接失败: {e}")
            return False

    async def _send_hello(self):
        """发送认证消息"""
        hello_kwargs = {}
        if self.secret:
            hello_kwargs["secret"] = self.secret

        hello_msg = {
            "wslink": "1.0",
            "id": "system:c0:0",
            "method": "wslink.hello",
            "args": [],
            "kwargs": hello_kwargs
        }

        return await self._send_message(hello_msg)

    async def _send_message(self, message):
        """发送消息并接收响应"""
        try:
            print(f"发送消息: {message}")

            # 使用 msgpack 序列化消息
            message_bytes = msgpack.packb(message)
            print(f"序列化后大小: {len(message_bytes)} 字节")

            # 生成分块消息
            chunks = list(generate_chunks(message_bytes, 0))  # 0 表示不分块
            print(f"生成 {len(chunks)} 个分块")

            # 发送所有分块
            for i, chunk in enumerate(chunks):
                print(f"发送分块 {i+1}/{len(chunks)}, 大小: {len(chunk)} 字节")
                await self.websocket.send(chunk)

            # 接收响应
            print("等待响应...")
            response = await asyncio.wait_for(self.websocket.recv(), timeout=10.0)
            print(f"收到响应，类型: {type(response)}, 大小: {len(response) if hasattr(response, '__len__') else 'N/A'}")

            if isinstance(response, bytes):
                # 处理分块响应
                result = self.unchunker.process_chunk(response)
                print(f"分块处理结果: {result}")
                return result
            else:
                # 如果是文本消息，尝试解析为 JSON
                result = json.loads(response)
                print(f"JSON 解析结果: {result}")
                return result

        except asyncio.TimeoutError:
            print("等待响应超时")
            return {"error": "响应超时"}
        except Exception as e:
            print(f"消息发送失败: {e}")
            import traceback
            traceback.print_exc()
            return {"error": str(e)}

    def _get_next_message_id(self):
        """获取下一个消息ID"""
        self.message_id += 1
        return f"rpc:{self.client_id}:{self.message_id}"

    async def call_rpc(self, method, *args, **kwargs):
        """调用 RPC 方法"""
        if not self.websocket:
            print("未连接到服务器")
            return {"error": "未连接到服务器"}

        message = {
            "wslink": "1.0",
            "id": self._get_next_message_id(),
            "method": method,
            "args": list(args),
            "kwargs": kwargs
        }

        print(f"调用 RPC: {method}")
        response = await self._send_message(message)

        if "result" in response:
            print(f"RPC 调用成功: {response['result']}")
            return response["result"]
        elif "error" in response:
            print(f"RPC 调用失败: {response['error']}")
            return {"error": response["error"]}
        else:
            print(f"未知响应: {response}")
            return response

    async def load_data_from_path(self, path):
        """加载数据的便捷方法"""
        return await self.call_rpc("load_data_from_path", path)

    async def close(self):
        """关闭连接"""
        if self.websocket:
            await self.websocket.close()
            print("连接已关闭")

async def test_rpc_client():
    """测试 RPC 客户端"""
    client = TrameRPCClient()

    try:
        # 连接到服务器
        if await client.connect():
            # 调用加载数据方法
            result = await client.load_data_from_path(
                "/home/<USER>/otherProjects/pymipf/.sourceData/img_boning"
            )
            print(f"加载数据结果: {result}")
        else:
            print("连接失败")

    except Exception as e:
        print(f"测试失败: {e}")

    finally:
        await client.close()

if __name__ == "__main__":
    print("=== Trame RPC 客户端测试 ===")
    asyncio.run(test_rpc_client())
