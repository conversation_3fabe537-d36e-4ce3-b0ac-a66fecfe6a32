
#!/usr/bin/env python3
"""
测试 trame WebSocket RPC 调用 - 使用二进制消息
"""

import asyncio
import websockets
import json
import sys
import struct

async def test_websocket_rpc():
    """测试 WebSocket RPC 调用"""
    uri = "ws://localhost:8083/ws"

    try:
        print(f"连接到 {uri}...")
        async with websockets.connect(uri) as websocket:
            print("WebSocket 连接成功!")

            # 1. 发送 hello 消息进行认证
            hello_msg = {
                "wslink": "1.0",
                "id": "system:c0:0",
                "method": "wslink.hello",
                "args": [],
                "kwargs": {"secret": "wslink-secret"}  # 使用认证密钥
            }

            print("发送 hello 消息...")
            # 将消息转换为二进制格式
            hello_json = json.dumps(hello_msg).encode('utf-8')
            await websocket.send(hello_json)

            # 接收 hello 响应
            hello_response = await websocket.recv()
            print(f"Hello 响应类型: {type(hello_response)}")

            # 处理二进制响应
            if isinstance(hello_response, bytes):
                hello_response_str = hello_response.decode('utf-8')
                print(f"Hello 响应: {hello_response_str}")
                hello_data = json.loads(hello_response_str)
            else:
                print(f"Hello 响应: {hello_response}")
                hello_data = json.loads(hello_response)

            if "error" in hello_data:
                print(f"认证失败: {hello_data['error']}")
                return

            # 2. 调用 load_data_from_path 方法
            load_msg = {
                "wslink": "1.0",
                "id": "rpc:client:1",
                "method": "load_data_from_path",
                "args": ["/home/<USER>/otherProjects/pymipf/.sourceData/img_boning"],
                "kwargs": {}
            }

            print("发送加载数据请求...")
            # 将消息转换为二进制格式
            load_json = json.dumps(load_msg).encode('utf-8')
            await websocket.send(load_json)

            # 接收响应
            load_response = await websocket.recv()
            print(f"加载响应类型: {type(load_response)}")

            # 处理二进制响应
            if isinstance(load_response, bytes):
                load_response_str = load_response.decode('utf-8')
                print(f"加载响应: {load_response_str}")
                load_data = json.loads(load_response_str)
            else:
                print(f"加载响应: {load_response}")
                load_data = json.loads(load_response)

            if "result" in load_data:
                print(f"加载成功: {load_data['result']}")
            elif "error" in load_data:
                print(f"加载失败: {load_data['error']}")

    except ConnectionRefusedError:
        print("连接被拒绝，请确保服务器正在运行在 localhost:8083")
    except Exception as e:
        print(f"连接错误: {e}")

async def test_simple_state_change():
    """测试简单的状态变更方法"""
    uri = "ws://localhost:8083/ws"

    try:
        print(f"连接到 {uri}...")
        async with websockets.connect(uri) as websocket:
            print("WebSocket 连接成功!")

            # 1. 发送 hello 消息
            hello_msg = {
                "wslink": "1.0",
                "id": "system:hello:0",
                "method": "wslink.hello",
                "args": [],
                "kwargs": {"secret": "wslink-secret"}
            }

            hello_json = json.dumps(hello_msg).encode('utf-8')
            await websocket.send(hello_json)
            hello_response = await websocket.recv()

            if isinstance(hello_response, bytes):
                hello_response_str = hello_response.decode('utf-8')
                print(f"Hello 响应: {hello_response_str}")
            else:
                print(f"Hello 响应: {hello_response}")

            # 2. 直接设置状态变量
            state_msg = {
                "wslink": "1.0",
                "id": "rpc:client:2",
                "method": "trame.state.update",
                "args": [],
                "kwargs": {
                    "data_path": "/home/<USER>/otherProjects/pymipf/.sourceData/img_boning"
                }
            }

            print("发送状态更新请求...")
            state_json = json.dumps(state_msg).encode('utf-8')
            await websocket.send(state_json)

            # 接收响应
            state_response = await websocket.recv()
            if isinstance(state_response, bytes):
                state_response_str = state_response.decode('utf-8')
                print(f"状态更新响应: {state_response_str}")
            else:
                print(f"状态更新响应: {state_response}")

    except Exception as e:
        print(f"错误: {e}")

if __name__ == "__main__":
    print("=== trame WebSocket RPC 测试 ===")

    if len(sys.argv) > 1 and sys.argv[1] == "state":
        print("\n测试状态变更...")
        asyncio.run(test_simple_state_change())
    else:
        print("\n测试 WebSocket RPC 调用...")
        asyncio.run(test_websocket_rpc())

