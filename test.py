
import asyncio
import websockets
import json

async def load_data_via_websocket(path):
    uri = "ws://localhost:8083/ws"
    
    async with websockets.connect(uri) as websocket:
        # 发送认证消息（如果需要）
        hello_msg = {
            "wslink": "1.0",
            "id": "system:hello:0",
            "method": "wslink.hello",
            "args": [],
            "kwargs": {"secret": "wslink-secret"}  # 如果有密钥的话
        }
        await websocket.send(json.dumps(hello_msg))
        response = await websocket.recv()
        print("Hello response:", response)
        
        # 调用加载数据方法
        load_msg = {
            "wslink": "1.0",
            "id": "rpc:client:1",
            "method": "load_data_from_path",
            "args": ["/home/<USER>/otherProjects/pymipf/.sourceData/img_boning"],
            "kwargs": {}
        }
        await websocket.send(json.dumps(load_msg))
        response = await websocket.recv()
        print("Load response:", response)

# 使用示例
asyncio.run(load_data_via_websocket("/home/<USER>/otherProjects/pymipf/.sourceData/img_boning"))

