#!/usr/bin/env python3
"""
简化的 wslink 客户端，基于浏览器消息格式
"""

import asyncio
import websockets
import msgpack
from wslink.chunking import generate_chunks, UnChunker

async def test_simple_rpc():
    """测试简单的 RPC 调用"""
    uri = "ws://localhost:8083/ws"

    try:
        print(f"连接到 {uri}...")
        async with websockets.connect(uri) as websocket:
            print("WebSocket 连接成功!")

            unchunker = UnChunker()

            # 尝试不同的密钥
            secrets_to_try = [
                "",  # 空密钥
                "wslink-secret",  # 默认密钥
                "vtkweb-secret",  # VTK 默认密钥
                None,  # 无密钥字段
            ]

            for i, secret in enumerate(secrets_to_try):
                print(f"\n=== 尝试密钥 {i+1}: {secret} ===")

                # 构造 hello 消息
                hello_kwargs = {}
                if secret is not None:
                    hello_kwargs["secret"] = secret

                hello_msg = {
                    "wslink": "1.0",
                    "id": f"system:hello:{i}",
                    "method": "wslink.hello",
                    "args": [],
                    "kwargs": hello_kwargs
                }

                print("发送 hello 消息...")
                print(f"消息内容: {hello_msg}")

                # 序列化并发送
                message_bytes = msgpack.packb(hello_msg)
                print(f"序列化后大小: {len(message_bytes)} 字节")

                # 生成分块
                chunks = list(generate_chunks(message_bytes, 0))
                print(f"生成 {len(chunks)} 个分块")

                for j, chunk in enumerate(chunks):
                    print(f"发送分块 {j+1}, 大小: {len(chunk)} 字节")
                    await websocket.send(chunk)

                # 接收响应
                print("等待 hello 响应...")
                hello_response = await asyncio.wait_for(websocket.recv(), timeout=10.0)
                print(f"收到响应，类型: {type(hello_response)}, 大小: {len(hello_response)}")

                if isinstance(hello_response, bytes):
                    hello_result = unchunker.process_chunk(hello_response)
                    print(f"Hello 响应: {hello_result}")

                    if hello_result and "error" in hello_result:
                        print(f"认证失败: {hello_result['error']}")
                        continue  # 尝试下一个密钥
                    elif hello_result and "result" in hello_result:
                        print("认证成功!")
                        client_id = hello_result.get("result", {}).get("clientID", "client")
                        print(f"客户端ID: {client_id}")
                        break  # 认证成功，跳出循环
                    else:
                        print("未知的 hello 响应格式")
                        continue
                else:
                    print("收到非二进制响应")
                    continue
            else:
                print("所有密钥都认证失败")
                return

            # 2. 调用 load_data_from_path
            load_msg = {
                "wslink": "1.0",
                "id": "rpc:client:1",
                "method": "load_data_from_path",
                "args": ["/home/<USER>/otherProjects/pymipf/.sourceData/img_boning"],
                "kwargs": {}
            }

            print("\n发送 load_data_from_path 请求...")
            print(f"消息内容: {load_msg}")

            # 序列化并发送
            load_bytes = msgpack.packb(load_msg)
            load_chunks = list(generate_chunks(load_bytes, 0))

            for chunk in load_chunks:
                await websocket.send(chunk)

            # 接收响应
            print("等待 load_data_from_path 响应...")
            load_response = await asyncio.wait_for(websocket.recv(), timeout=15.0)

            if isinstance(load_response, bytes):
                load_result = unchunker.process_chunk(load_response)
                print(f"Load 响应: {load_result}")

                if load_result and "result" in load_result:
                    print(f"加载成功: {load_result['result']}")
                elif load_result and "error" in load_result:
                    print(f"加载失败: {load_result['error']}")
                else:
                    print("未知的 load 响应格式")
            else:
                print("收到非二进制响应")

    except asyncio.TimeoutError:
        print("操作超时")
    except Exception as e:
        print(f"错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    print("=== 简化 wslink 客户端测试 ===")
    asyncio.run(test_simple_rpc())
