#!/usr/bin/env python3
"""
分析客户端发送的消息格式
"""

import msgpack
from wslink.chunking import _decode_header

# 从浏览器截图中提取的十六进制数据
hex_data = """
43 82 F9 9E 00 00 00 00 55 00 00 00 85 A6 77 73 6C 69 6E 6B A3 31 2E 30 A2 69 64 AB
73 79 73 74 65 6D 3A 68 65 6C 6C 6F 3A 30 A6 6D 65 74 68 6F 64 AB 77 73 6C 69 6E 6B
2E 68 65 6C 6C 6F A4 61 72 67 73 90 A6 6B 77 61 72 67 73 81 A6 73 65 63 72 65 74 AB
77 73 6C 69 6E 6B 2D 73 65 63 72 65 74
""".replace('\n', '').replace(' ', '')

# 转换为字节
data = bytes.fromhex(hex_data)
print(f"总数据长度: {len(data)} 字节")

# 解析头部 (前12字节)
header = data[:12]
print(f"头部: {header.hex()}")

# 解码头部
id, offset, total_size = _decode_header(header)
print(f"消息ID: {id}")
print(f"偏移量: {offset}")
print(f"总大小: {total_size}")

# 解析消息内容
message_data = data[12:]
print(f"消息数据长度: {len(message_data)} 字节")
print(f"消息数据: {message_data.hex()}")

# 使用 msgpack 解包
unpacked = None
try:
    unpacked = msgpack.unpackb(message_data, strict_map_key=False)
    print(f"解包后的消息: {unpacked}")
except Exception as e:
    print(f"解包失败: {e}")

# 分析消息结构
print("\n=== 消息结构分析 ===")
if unpacked and isinstance(unpacked, dict):
    for key, value in unpacked.items():
        print(f"{key}: {value}")

# 尝试手动构造相同的消息
print("\n=== 构造相同格式的消息 ===")
test_message = {
    "wslink": "1.0",
    "id": "system:hello:0",
    "method": "wslink.hello",
    "args": [],
    "kwargs": {"secret": "wslink-secret"}
}

test_packed = msgpack.packb(test_message)
print(f"测试消息打包后: {test_packed.hex()}")
print(f"测试消息长度: {len(test_packed)} 字节")

# 比较两个消息
print(f"原始消息: {message_data.hex()}")
print(f"测试消息: {test_packed.hex()}")
print(f"是否相同: {message_data == test_packed}")

# 找出差异
print("\n=== 差异分析 ===")
for i, (a, b) in enumerate(zip(message_data, test_packed)):
    if a != b:
        print(f"位置 {i}: 原始={a:02x} 测试={b:02x}")

# 尝试解包测试消息
print("\n=== 解包测试消息 ===")
try:
    test_unpacked = msgpack.unpackb(test_packed, strict_map_key=False)
    print(f"测试消息解包成功: {test_unpacked}")
except Exception as e:
    print(f"测试消息解包失败: {e}")

# 尝试截取原始消息的前85字节
print("\n=== 尝试截取原始消息 ===")
try:
    truncated = message_data[:85]
    truncated_unpacked = msgpack.unpackb(truncated, strict_map_key=False)
    print(f"截取消息解包成功: {truncated_unpacked}")
except Exception as e:
    print(f"截取消息解包失败: {e}")

# 分析字符串编码差异
print("\n=== 字符串编码分析 ===")
# 查看 "system:hello:0" 的编码
original_id_bytes = bytes.fromhex("ab73797374656d3a68656c6c6f3a30")
test_id_bytes = bytes.fromhex("ae73797374656d3a68656c6c6f3a30")
print(f"原始ID编码: {original_id_bytes}")
print(f"测试ID编码: {test_id_bytes}")

# 查看 "wslink.hello" 的编码
original_method_bytes = bytes.fromhex("ab77736c696e6b2e68656c6c6f")
test_method_bytes = bytes.fromhex("ac77736c696e6b2e68656c6c6f")
print(f"原始方法编码: {original_method_bytes}")
print(f"测试方法编码: {test_method_bytes}")

# 查看 "wslink-secret" 的编码
original_secret_bytes = bytes.fromhex("ab77736c696e6b2d736563726574")
test_secret_bytes = bytes.fromhex("ad77736c696e6b2d736563726574")
print(f"原始密钥编码: {original_secret_bytes}")
print(f"测试密钥编码: {test_secret_bytes}")

# 检查原始消息是否有额外数据
print(f"\n=== 检查额外数据 ===")
print(f"头部声明的大小: {total_size}")
print(f"实际消息数据长度: {len(message_data)}")
if len(message_data) > total_size:
    extra_data = message_data[total_size:]
    print(f"额外数据: {extra_data.hex()}")
    print(f"额外数据长度: {len(extra_data)}")

# 尝试只解包声明大小的数据
print(f"\n=== 解包声明大小的数据 ===")
try:
    declared_data = message_data[:total_size]
    declared_unpacked = msgpack.unpackb(declared_data, strict_map_key=False)
    print(f"声明大小数据解包成功: {declared_unpacked}")
except Exception as e:
    print(f"声明大小数据解包失败: {e}")

# 创建一个与原始消息完全相同的测试
print(f"\n=== 创建兼容的消息 ===")
# 使用原始消息的确切格式
compatible_message = declared_unpacked if 'declared_unpacked' in locals() else test_message
print(f"兼容消息: {compatible_message}")

# 现在我们知道了正确的格式，让我们更新客户端
