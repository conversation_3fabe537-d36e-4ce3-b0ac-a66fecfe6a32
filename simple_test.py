#!/usr/bin/env python3
"""
简单的状态变量测试 - 通过设置状态变量来触发数据加载
"""

import asyncio
import websockets
import json

async def test_state_update():
    """通过状态更新来触发数据加载"""
    uri = "ws://localhost:8083/ws"
    
    try:
        print(f"连接到 {uri}...")
        async with websockets.connect(uri) as websocket:
            print("WebSocket 连接成功!")
            
            # 1. 发送 hello 消息（简化版本）
            hello_msg = {
                "wslink": "1.0",
                "id": "system:hello:0",
                "method": "wslink.hello",
                "args": [],
                "kwargs": {}
            }
            
            print("发送 hello 消息...")
            await websocket.send(json.dumps(hello_msg))
            
            # 等待响应
            try:
                hello_response = await asyncio.wait_for(websocket.recv(), timeout=5.0)
                print(f"Hello 响应: {hello_response}")
                
                hello_data = json.loads(hello_response)
                if "error" in hello_data:
                    print(f"认证失败: {hello_data['error']}")
                    return
                    
            except asyncio.TimeoutError:
                print("Hello 响应超时，尝试继续...")
            
            # 2. 直接设置状态变量来触发数据加载
            state_update_msg = {
                "wslink": "1.0",
                "id": "rpc:client:1",
                "method": "trame.state.update",
                "args": [],
                "kwargs": {
                    "data_path": "/home/<USER>/otherProjects/pymipf/.sourceData/img_boning"
                }
            }
            
            print("发送状态更新请求...")
            await websocket.send(json.dumps(state_update_msg))
            
            # 等待响应
            try:
                state_response = await asyncio.wait_for(websocket.recv(), timeout=10.0)
                print(f"状态更新响应: {state_response}")
                
                state_data = json.loads(state_response)
                if "result" in state_data:
                    print("状态更新成功!")
                elif "error" in state_data:
                    print(f"状态更新失败: {state_data['error']}")
                    
            except asyncio.TimeoutError:
                print("状态更新响应超时")
                
    except ConnectionRefusedError:
        print("连接被拒绝，请确保服务器正在运行在 localhost:8083")
    except Exception as e:
        print(f"连接错误: {e}")

if __name__ == "__main__":
    print("=== 简单状态变量测试 ===")
    asyncio.run(test_state_update())
